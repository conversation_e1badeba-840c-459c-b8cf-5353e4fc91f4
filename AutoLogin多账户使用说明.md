# AutoLogin 多账户功能使用说明

## 功能概述

AutoLogin模块已升级为支持多账户自动登录功能。模块会自动检测当前MC客户端登录的用户名，并根据预设的账户列表自动匹配对应的密码进行登录。

## 主要特性

1. **自动用户名检测** - 自动检测当前MC客户端的用户名
2. **多账户支持** - 支持配置多个用户名-密码对
3. **自动匹配** - 根据当前用户名自动选择对应密码
4. **调试模式** - 提供详细的调试信息帮助排查问题

## 配置方法

### 1. 基本设置

在Meteor客户端中找到"自动登录"模块，你会看到以下设置选项：

- **当前用户名**: 显示当前检测到的MC用户名（只读）
- **调试模式**: 开启后会显示详细的调试信息
- **账户列表**: 配置用户名和密码对

### 2. 账户列表配置

在"账户管理"分组中，你可以配置最多5个账户。每个账户都有独立的用户名和密码设置：

- **账户1-用户名** / **账户1-密码**
- **账户2-用户名** / **账户2-密码**
- **账户3-用户名** / **账户3-密码**
- **账户4-用户名** / **账户4-密码**
- **账户5-用户名** / **账户5-密码**

**配置方法：**
1. 在对应的"账户X-用户名"字段中输入MC用户名
2. 在对应的"账户X-密码"字段中输入该账户的登录密码
3. 不需要的账户可以留空

**示例配置：**
- 账户1-用户名: `Steve`
- 账户1-密码: `mypassword123`
- 账户2-用户名: `Alex`
- 账户2-密码: `alexpass456`
- 其他账户留空

**注意事项：**
- 用户名和密码区分大小写
- 空的账户设置会被自动忽略
- 用户名和密码前后的空格会被自动去除

### 3. 调试模式

建议在首次配置时开启调试模式，这样可以看到：
- 当前检测到的用户名
- 已配置的账户数量
- 登录时的详细信息
- 错误信息（如果有）

## 工作原理

1. **激活时**: 模块会检测当前用户名并解析账户列表
2. **收到登录提示时**: 
   - 检测当前用户名
   - 在账户列表中查找匹配的密码
   - 如果找到匹配的密码，自动执行登录命令
   - 如果未找到匹配的账户，在调试模式下会显示警告

## 使用步骤

1. 打开Meteor客户端设置
2. 找到"自动登录"模块
3. 开启"调试模式"（推荐）
4. 在"账户管理"分组中配置你的账户：
   - 在"账户1-用户名"中输入第一个账户的用户名
   - 在"账户1-密码"中输入对应的密码
   - 根据需要配置更多账户（最多5个）
5. 激活模块
6. 进入需要登录的服务器，模块会自动处理登录

## 故障排除

### 问题：模块没有自动登录
**解决方案：**
1. 检查调试模式是否显示了正确的用户名
2. 确认账户列表格式是否正确
3. 确认当前用户名是否在账户列表中

### 问题：显示"未找到用户的密码配置"
**解决方案：**
1. 检查当前用户名是否与配置的账户用户名完全匹配（区分大小写）
2. 确认对应账户的用户名和密码字段都已正确填写
3. 在调试模式下查看"可用账户"列表，确认账户已正确加载

### 问题：获取用户名失败
**解决方案：**
1. 确保MC客户端已正常登录
2. 重启客户端后重试

## 安全提示

- 密码信息存储在客户端配置中，请确保你的设备安全
- 不要在不信任的环境中使用此功能
- 定期更改密码以确保账户安全

## 更新日志

### v2.0
- 添加多账户支持（最多5个账户）
- 自动检测当前用户名
- 添加调试模式
- 改进用户界面，使用独立的用户名/密码字段
- 移除单一密码设置，改为列表式账户管理
- 增强错误提示和调试信息
