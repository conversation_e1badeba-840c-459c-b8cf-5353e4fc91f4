# AutoLogin 模块测试指南

## 测试准备

1. 确保Meteor客户端已正确安装并运行
2. 确保AutoLogin模块已添加到项目中
3. 准备测试用的MC账户信息

## 测试步骤

### 1. 基本功能测试

1. **启动测试**
   - 打开Meteor客户端
   - 找到"自动登录"模块
   - 开启"调试模式"

2. **用户名检测测试**
   - 激活模块
   - 查看聊天框中的调试信息
   - 确认显示了正确的当前用户名

3. **账户配置测试**
   - 在"账户管理"分组中配置测试账户：
     - 账户1-用户名: 输入当前MC用户名
     - 账户1-密码: 输入测试密码
   - 重新激活模块
   - 确认调试信息显示"已配置账户数量: 1"

### 2. 登录功能测试

1. **模拟登录提示**
   - 进入需要登录的服务器
   - 或者手动发送包含"/login"的消息来触发
   - 观察模块是否自动执行登录命令

2. **多账户测试**
   - 配置多个不同的账户
   - 使用不同的MC账户登录客户端
   - 测试模块是否能正确匹配对应的密码

### 3. 错误处理测试

1. **未配置账户测试**
   - 清空所有账户配置
   - 激活模块
   - 确认调试信息显示"已配置账户数量: 0"
   - 触发登录，确认显示"未找到用户的密码配置"

2. **用户名不匹配测试**
   - 配置一个与当前用户名不匹配的账户
   - 触发登录
   - 确认显示相应的警告信息

## 预期结果

### 正常情况
- 模块激活时显示当前用户名
- 正确加载配置的账户数量
- 收到登录提示时自动执行对应的登录命令
- 调试信息清晰显示操作过程

### 异常情况
- 未找到匹配账户时显示警告
- 显示当前用户名和可用账户列表
- 不会执行错误的登录命令

## 调试信息示例

```
[INFO] 自动登录模块已激活
[INFO] 当前用户名: TestUser
[INFO] 已配置账户数量: 2
[INFO] 已更新账户列表，共 2 个账户
[INFO] 已添加账户: TestUser
[INFO] 已添加账户: AnotherUser
[INFO] 为用户 TestUser 自动登录
```

## 常见问题

1. **模块未激活**
   - 检查模块是否已正确添加到AddonTemplate中
   - 确认没有编译错误

2. **用户名检测失败**
   - 确认MC客户端已正常登录
   - 检查getSession()方法是否可用

3. **登录命令未执行**
   - 确认收到的消息包含"/login"
   - 检查网络连接是否正常

## 性能测试

- 测试模块对游戏性能的影响
- 确认不会造成延迟或卡顿
- 验证内存使用是否合理

## 安全测试

- 确认密码信息不会泄露到日志中
- 验证调试信息的安全性
- 测试异常情况下的数据保护
